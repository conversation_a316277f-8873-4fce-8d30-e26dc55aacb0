<template>
  <div
    class="relative fx-cer flex-col min-h-screen px-4 pb-12 overflow-hidden"
    :class="{ 'pt-10': isMobile }"
  >
    <!-- 背景图 -->
    <img src="~/assets/image/bgTri.png" alt="" class="absolute top-0 left-0 w-100 h-100" />
    <img
      src="~/assets/image/bgCircle.png"
      alt=""
      class="absolute bottom-[-160px] right-0 w-100 h-100"
    />

    <!-- 错误提示区域 -->
    <div v-if="showError" class="w-full max-w-[760px] mb-6">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-400">
              Search Error
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              {{ error }}
            </div>
          </div>
          <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
              <button @click="clearError" class="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600">
                <span class="sr-only">Dismiss</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据卡片展示区域 -->
    <div v-if="showResults" class="w-full max-w-[760px] space-y-4 mb-6">
      <div class="talent-card-wrapper">
        <SearchCard :candidates="cards" />
      </div>
    </div>

    <!-- 原始 Logo 区域 -->
    <div v-else class="flex flex-col items-center mb-6 text-center logo-area">
      <img
        :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
        class="w-[180px] sm:w-[240px] h-auto mb-2"
        :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
      />
      <div class="text-[#5E5E5E] text-sm sm:text-5 font-400">
        Intelligent matching of top talent
      </div>
    </div>

    <!-- 轮播区域 -->
    <div
      v-if="!showResults"
      class="h-auto mb-6 rounded-4 px-4 py-2 fx-cer gap-4 bg-gray-100"
      :class="{ 'dark:bg-[#242425]': users.length > 0 }"
    >
      <span>Poach Window</span>
      <Transition name="fade">
        <div>
          <div
            v-if="users.length > 0"
            class="flex flex-wrap items-center justify-center gap-3 text-orange-500 cursor-pointer text-sm sm:text-4 animate__animated animate__fadeInDown"
            @click="handleUserClick(currentCarouselUser)"
            :key="currentCarouselUser?.id"
          >
            <img
              :src="currentCarouselUser.avatarUrl"
              alt="Avatar"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>{{ currentCarouselUser.name }}</span>
            <img
              :src="currentCarouselUser.oldCompanyIcon"
              alt="Old"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>→</span>
            <img
              :src="currentCarouselUser.newCompanyIcon"
              alt="New"
              class="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
            />
            <span>${{ currentCarouselUser.salary }}</span>
          </div>
        </div>
      </Transition>
    </div>
    <!-- 搜索框 -->
    <div
      class="w-full max-w-[760px] h-auto border border-black rounded-[10px] bg-white px-4 py-3 dark:bg-[#242425] z-10"
    >
      <input
        v-model="inputValue"
        class="w-full px-2 py-2 text-sm sm:text-base rounded focus:outline-none focus:border-none mb-2 dark:bg-[#242425]"
        placeholder="AI Agent"
      />
      <div class="flex flex-wrap items-center justify-between gap-3">
        <!-- 筛选按钮 -->
        <div class="relative inline-block text-left">
          <button
            @click="toggleDropdown"
            class="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-200 bg-transparent dark:hover:bg-gray-200/20 border border-gray-300 dark:border-gray-600"
          >
            <img :src="getCurrentIcon()" alt="" class="w-4 h-4" />
            <span class="text-sm text-gray-700 dark:text-gray-300">{{ selected }}</span>
            <svg class="w-4 h-4 text-gray-500 transition-transform" :class="{ 'rotate-180': dropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <!-- 筛选下拉菜单 -->
          <div
            v-if="dropdownOpen && !isMobile"
            class="absolute z-10 mt-2 w-44 flex flex-col gap-2 bg-white p-2 border border-[#DDD] dark:border-[#252525] rounded-2 shadow-md dark:bg-[#1B1B1B]"
          >
            <div
              v-for="option in options"
              :key="option.value"
              @click="toggleSelection(option.value)"
              class="px-4 py-2 flex items-center gap-2 cursor-pointer w-full rounded-2"
              :class="{
                'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                  selected === option.value,
                'bg-transparent hover:bg-gray-100 text-gray-700 dark:text-gray-300 dark:hover:bg-gray-700':
                  selected !== option.value,
              }"
            >
              <img :src="option.icon" alt="" class="w-4 h-4" />
              <span class="text-sm">{{ option.label }}</span>
              <svg v-if="selected === option.value" class="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 移动端底部弹出 -->
        <Transition name="fade">
          <div
            v-if="dropdownOpen && isMobile"
            class="fixed bottom-0 left-0 right-0 bg-white dark:bg-[#1B1B1B] z-50 rounded-t-[20px] p-4 border-t border-[#DDD] dark:border-[#2B2B2B] shadow-xl"
          >
            <!-- 关闭按钮 -->
            <div class="flex justify-end mb-4">
              <button
                class="text-gray-500 dark:text-gray-300 bg-transparent"
                @click="dropdownOpen = false"
              >
                <img src="~/assets/image/close-middle.svg" alt="" class="w-5 h-5" />
              </button>
            </div>

            <!-- 筛选项内容 -->
            <div class="flex flex-col gap-2">
              <div
                v-for="option in options"
                :key="option.value"
                @click="toggleSelection(option.value)"
                class="px-4 py-3 flex items-center gap-2 cursor-pointer rounded-2"
                :class="{
                  'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                    selected === option.value,
                  'bg-white border border-[#E6E6E6] hover:bg-gray-50 text-gray-700 dark:border-none dark:bg-[#212121] dark:hover:bg-[#353535] dark:text-[#7A7A7A]':
                    selected !== option.value,
                }"
              >
                <img :src="option.icon" alt="" />
                <span>{{ option.label }}</span>
              </div>
            </div>
          </div>
        </Transition>

        <!-- 提交按钮 -->
        <button
          class="btn btn-primary w-[70px] h-[36px] rounded-2"
          :class="{
            'bg-[#1C1C21] text-white': isInputValid,
            'bg-[#E5E7EB] cursor-not-allowed text-[#9CA3AF] dark:bg-[#374151] dark:text-[#6B7280]': !isInputValid,
          }"
          :disabled="!isInputValid"
          @click="submitQuery"
        >
          DINQ
        </button>
      </div>
    </div>

    <!-- 加载弹窗 -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div
        class="bg-white dark:bg-black dark:border-[#27282D] p-6 rounded-lg shadow-xl w-[90%] max-w-[360px] h-[198px] flex flex-col items-center justify-center gap-2 text-center"
      >
        <p class="text-base font-bold sm:text-6">DINQing</p>
        <p class="text-[#585858] text-sm">Searching talent</p>
        <img
          src="~/assets/image/gif2.gif"
          alt="loading"
          class="btn-icon-dark w-16 h-16 sm:w-20 sm:h-20"
        />
        <img
          src="~/assets/image/gif.gif"
          alt="loading"
          class="btn-icon-light w-16 h-16 sm:w-20 sm:h-20"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, defineExpose } from 'vue'
  import slider from '/assets/image/slider.png'
  import SearchCard from '../../components/SearchCard/index.vue'
  import github from '/assets/image/github 1.svg'
  import scholar from '/assets/image/scholar 1.svg'
  import company from '/assets/image/office-building 1.svg'
  import 'animate.css'
  // 控制状态
  const dropdownOpen = ref(false)
  const selected = ref<string>('Scholar')
  const inputValue = ref('')
  const loading = ref(false)
  const showResults = ref(false)
  const cards = ref<Card[]>([])
  // 错误状态管理
  const error = ref('')
  const showError = ref(false)
  // 检测深色模式
  const isDark = ref(false)
  const isMobile = ref(false)
  const operator = ref(0)

  onMounted(() => {
    const checkMobile = () => {
      isMobile.value = window.innerWidth <= 768
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
  })

  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  // 搜索选项配置
  const options = [
    { label: 'Scholar', value: 'Scholar', icon: scholar },
    { label: 'GitHub', value: 'GitHub', icon: github },
    { label: 'Company', value: 'Company', icon: company },
  ]

  // 是否允许提交
  const isInputValid = computed(() => {
    return inputValue.value.trim() !== ''
  })

  // 获取当前选中项的图标
  const getCurrentIcon = () => {
    const currentOption = options.find(option => option.value === selected.value)
    return currentOption ? currentOption.icon : scholar
  }

  // 切换下拉菜单
  const toggleDropdown = () => {
    dropdownOpen.value = !dropdownOpen.value
  }

  // 切换选择项
  const toggleSelection = (value: string) => {
    selected.value = value // 单选：只保留当前选中的项
    dropdownOpen.value = false // 选择后关闭下拉菜单
  }

  // 清除错误信息
  const clearError = () => {
    error.value = ''
    showError.value = false
  }

  // 显示错误信息
  const showErrorMessage = (message: string) => {
    error.value = message
    showError.value = true
    showResults.value = false
  }

  // 提交查询
  const submitQuery = async () => {
    if (!isInputValid.value) return

    // 清除之前的错误
    clearError()

    // 检查用户是否已登录
    if (!currentUser.value) {
      showErrorMessage('Please log in to search for talent. Authentication is required to access our database.')
      return
    }

    loading.value = true
    showResults.value = false

    try {
      // 调用真实API
      const apiResponse = await fetchData(inputValue.value, selected.value)

      if (!apiResponse || apiResponse.length === 0) {
        showErrorMessage(`No results found for "${inputValue.value}" in ${selected.value}. Try different keywords or search category.`)
        return
      }

      // 使用transformApiResponse函数处理数据转换
      const transformedCandidates = apiResponse

      cards.value = transformedCandidates
      showResults.value = true
    } catch (err: any) {
      console.error('Search failed:', err)

      // 根据错误类型显示不同的错误信息
      if (err.message?.includes('401') || err.message?.includes('unauthorized')) {
        showErrorMessage('Authentication failed. Please log out and log in again.')
      } else if (err.message?.includes('403') || err.message?.includes('forbidden')) {
        showErrorMessage('Access denied. You may not have permission to perform this search.')
      } else if (err.message?.includes('429') || err.message?.includes('rate limit')) {
        showErrorMessage('Too many requests. Please wait a moment before searching again.')
      } else if (err.message?.includes('500') || err.message?.includes('server error')) {
        showErrorMessage('Server error occurred. Please try again later.')
      } else if (err.message?.includes('network') || err.message?.includes('fetch')) {
        showErrorMessage('Network error. Please check your internet connection and try again.')
      } else {
        showErrorMessage('Search failed. Please try again or contact support if the problem persists.')
      }
    } finally {
      loading.value = false
      inputValue.value = ''
    }
  }

  // Firebase Auth
  const { currentUser, authInitialized } = useFirebaseAuth()

  // 真实 API 请求函数
  const fetchData = async (query: string, filters: string): Promise<Card[]> => {
    if (!currentUser.value) {
      throw new Error('User not authenticated')
    }

    try {
      // 构建查询参数
      const categoryMap: Record<string, string> = {
        'Scholar': 'type:scholar',
        'GitHub': 'type:github',
        'Company': 'group:company'
      }

      const category = categoryMap[filters] || 'type:scholar'
      const searchQuery = `${category} ${query.trim()}`

      // 构建API URL
      const url = new URL('/api/v1/talent/search', window.location.origin)
      url.searchParams.append('query', searchQuery)
      url.searchParams.append('code', '64696E71')

      console.log('Making search API request:', {
        url: url.toString(),
        query: searchQuery,
        category,
        filters
      })

      // 发送请求
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${currentUser.value.uid}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorData.error || errorMessage
        } catch {
          const errorText = await response.text()
          errorMessage = errorText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const apiData = await response.json()
      console.log('API Response:', apiData)

      // 转换API响应为前端期望的格式
      return transformApiResponse(apiData, filters)

    } catch (error) {
      console.error('API request error:', error)
      throw error
    }
  }

  // 数据转换函数
  const transformApiResponse = (apiData: any, searchType: string): Card[] => {
    if (!apiData?.data || !Array.isArray(apiData.data)) {
      console.warn('Invalid API response format:', apiData)
      return []
    }

    return apiData.data.map((item: any) => {
      const data = item.data || {}
      const profile = item.profile || {}

      // 根据搜索类型和builder类型决定数据映射
      const isGitHub = item.builder === 'github' || searchType === 'GitHub'
      const isCompany = item.group === 'company' || searchType === 'Company'

      // 处理不同数据源的字段映射
      let title, venue, status, year

      if (isGitHub) {
        // GitHub数据映射
        title = data.repository || `${data.login}'s Repository` || 'GitHub Repository'
        venue = 'GitHub'
        status = data.stars ? `⭐ ${data.stars} stars` : 'Repository'
        year = String(new Date(data.created_at || Date.now()).getFullYear())
      } else {
        // Scholar数据映射
        title = data.article || data.name || 'Research Work'
        venue = data.venue || 'Research'
        status = data.status || 'Published'
        year = String(data.year || new Date().getFullYear())
      }

      // 统一的名称和机构处理
      const name = data.name || profile.name || 'Unknown'
      const position = data.position || profile.position || 'Unknown Position'
      const institution = data.company || profile.company || data.university || profile.university || 'Unknown Institution'
      const avatarUrl = data.avatar_url || profile.avatar_url || '/image/avator.png'
      const researchAreas = data.research_areas || data.tags || profile.research_areas || ['General']

      return {
        // 保留原始API数据结构，供Network按钮使用
        builder: item.builder,
        group: item.group,
        data: data,
        profile: profile,

        // 同时提供转换后的数据，供SearchCard显示使用
        data_type: isGitHub ? 'github' : 'paper',
        id: data.id || profile.id || '',
        name: name,
        positionTitle: position,
        institution: institution,
        avatarUrl: avatarUrl,
        skills: researchAreas,
        summary: profile.summary || '', // 从profile中获取summary
        featuredWork: {
          title,
          venue,
          type: status,
          year,
        },
        recommendations: ['Matched based on search criteria'],
        matchScore: 85, // 默认分数，实际应该从API获取

        // 兼容旧格式
        author_ids: data.id || profile.id || '',

        // 原始数据结构（保留用于调试）
        _originalData: {
          id: data.id || profile.id || '',
          title,
          authors: [name],
          author_ids: [data.id || profile.id || ''],
          keywords: researchAreas,
          primary_area: researchAreas,
          position: [position],
          aff: [institution],
          status,
          year,
          source: venue,
          profiles: [{
            author: name,
            author_id: data.id || profile.id || '',
            scholar_id: data.scholar || profile.scholar || '',
            avatar_url: avatarUrl,
            position,
            affiliation: institution,
            gender: profile.gender || 'Unknown',
          }],
          tags: researchAreas,
          author_info: {
            author: name,
            author_id: data.id || profile.id || '',
            scholar_id: data.scholar || profile.scholar || '',
            avatar_url: avatarUrl,
            position,
            affiliation: institution,
            gender: profile.gender || 'Unknown',
            score: Math.floor(Math.random() * 40) + 60, // 随机分数 60-100
            recommend_reason: generateRecommendations(data, isGitHub, isCompany),
          },
        },
      }
    }).filter((item: any) => item.id) // 过滤掉没有ID的无效数据
  }

  // 生成推荐理由
  const generateRecommendations = (data: any, isGitHub: boolean, isCompany: boolean): string[] => {
    const recommendations = []

    if (isGitHub) {
      recommendations.push('💻 Strong technical skills demonstrated through GitHub contributions')

      // 基于GitHub特定数据的推荐
      if (data.stars && data.stars > 1000) {
        recommendations.push('⭐ Maintains popular open-source projects with significant community impact')
      } else if (data.stars && data.stars > 100) {
        recommendations.push('⭐ Active contributor to well-regarded open-source projects')
      }

      if (data.bio && data.bio.length > 20) {
        recommendations.push('📝 Clear communication skills evident in detailed profile')
      }

      if (data.login) {
        recommendations.push('🔗 Established presence in the developer community')
      }
    } else {
      recommendations.push('🎓 Strong academic background with published research')

      // 基于Scholar特定数据的推荐
      if (data.status === 'Oral') {
        recommendations.push('🎤 Recognized research with oral presentation at top venues')
      } else if (data.status === 'Poster') {
        recommendations.push('📊 Active researcher presenting work at academic conferences')
      }

      if (data.research_areas && data.research_areas.length > 2) {
        recommendations.push('🔬 Interdisciplinary expertise across multiple research areas')
      }

      if (data.venue && ['ICML', 'NeurIPS', 'ICLR', 'AAAI'].includes(data.venue.toUpperCase())) {
        recommendations.push('🏆 Published at top-tier AI/ML conferences')
      }

      if (data.year && data.year >= new Date().getFullYear() - 1) {
        recommendations.push('🆕 Recent contributions to cutting-edge research')
      }
    }

    if (isCompany) {
      recommendations.push('🏢 Industry experience at leading technology companies')

      // 基于公司数据的推荐
      if (data.company && ['Google', 'Microsoft', 'Meta', 'OpenAI', 'Apple', 'Amazon'].some(company =>
          data.company.toLowerCase().includes(company.toLowerCase()))) {
        recommendations.push('🌟 Experience at top-tier technology companies')
      }
    }

    // 基于职位的推荐
    if (data.position) {
      const position = data.position.toLowerCase()
      if (position.includes('professor') || position.includes('faculty')) {
        recommendations.push('👨‍🏫 Academic leadership and teaching experience')
      } else if (position.includes('senior') || position.includes('lead') || position.includes('principal')) {
        recommendations.push('👑 Senior-level expertise and leadership experience')
      } else if (position.includes('researcher') || position.includes('scientist')) {
        recommendations.push('🔬 Dedicated research focus and scientific methodology')
      }
    }

    // 添加通用推荐理由（如果还需要更多）
    const genericReasons = [
      '🌟 Collaborative team player with strong problem-solving abilities',
      '🚀 Innovative thinker who brings fresh perspectives to complex challenges',
      '📈 Proven track record of delivering high-quality results',
      '🤝 Excellent communication and leadership skills',
      '⚡ Quick learner who adapts well to new technologies and methodologies',
      '🎯 Detail-oriented professional with strong analytical skills'
    ]

    // 如果推荐理由不足4个，添加通用理由
    while (recommendations.length < 4 && genericReasons.length > 0) {
      const randomIndex = Math.floor(Math.random() * genericReasons.length)
      recommendations.push(genericReasons.splice(randomIndex, 1)[0])
    }

    return recommendations.slice(0, 4) // 最多返回4个推荐理由
  }

  // 清空数据的方法
  const resetData = () => {
    inputValue.value = ''
    selected.value = ''
    showResults.value = false
  }

  // 定义卡片类型 - 与SearchCard期望的数据结构匹配
  interface Card {
    // 原始API数据结构，供Network按钮使用
    builder?: string
    group?: string
    data?: any
    profile?: any

    // 转换后的数据，供SearchCard显示使用
    data_type?: string
    id: string
    name: string
    positionTitle: string
    institution: string
    avatarUrl: string
    skills: string[]
    featuredWork: {
      title: string
      venue: string
      type: string
      year: string
    }
    recommendations: string[]
    matchScore: number

    // 兼容旧格式
    author_ids?: string

    // 原始数据结构（保留用于调试）
    _originalData?: any
  }

  // 轮播用户
  interface User {
    id: string
    avatarUrl: string
    name: string
    oldCompanyIcon: string
    newCompanyIcon: string
    salary: string
  }
  // 用户数据
  const users = ref<User[]>([])
  const currentIndex = ref(0)
  const currentCarouselUser = computed(() => {
    return users.value[currentIndex.value] || {}
  })
  // 动画方向控制（可选，用于切换方向）
  const transitionName = ref('animate__fadeInUp')

  // 真实数据替换为url，此行可删除
  import avator from '~/assets/image/avator.png'
  // 获取用户列表
  const fetchUserList = async (): Promise<User[]> => {
    // 替换为你自己的 API 请求逻辑
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          {
            avatarUrl: avator,
            id: '1',
            name: 'Alice Alen',
            oldCompanyIcon: avator,
            newCompanyIcon: avator,
            salary: '500M',
          },
          {
            id: '2',
            avatarUrl: avator,
            name: 'Grace Markson',
            oldCompanyIcon: avator,
            newCompanyIcon: avator,
            salary: '600M',
          },
          // 可以添加更多数据
        ])
      }, 1000)
    })
  }

  // 初始化：加载用户列表并启动轮播
  onMounted(async () => {
    users.value = await fetchUserList()

    if (users.value.length > 0) {
      setInterval(() => {
        transitionName.value = 'animate__fadeInUp' // 也可以随机切换不同动画
        currentIndex.value = (currentIndex.value + 1) % users.value.length
      }, 20000)
    }
  })

  // 用户点击
  const handleUserClick = (user: User) => {
    console.log('Clicked user:', user)
  }

  defineExpose({ resetData })
</script>

<style scoped>
  .logo-area {
    margin-top: 20%;
  }

  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: block;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    transform: translateY(100%);
    opacity: 0;
  }
  .fade-enter-to,
  .fade-leave-from {
    transform: translateY(0%);
    opacity: 1;
  }

  @media (max-width: 768px) {
    .logo-area {
      margin-top: 40%;
    }
  }
</style>
